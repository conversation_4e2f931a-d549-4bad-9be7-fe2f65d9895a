// Sidebar.tsx
"use client";

import React, { ReactNode, useState } from "react";
import {
  Package as PackageIcon,
  Briefcase,
  Star,
  Building,
  BookOpenText,
  ChevronLeft,
  User,
  LayoutDashboard,
  MessageCircle,
  LogOut,
} from "lucide-react";
import { MenuKey } from "./types";
import { signOut } from "next-auth/react";

interface SidebarProps {
  open: boolean;
  onToggle: () => void;
  selectedMenu: <PERSON>uKey;
  setSelectedMenu: (key: MenuKey) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  open,
  onToggle,
  selectedMenu,
  setSelectedMenu,
}) => {
  const handleLogout = async () => {
    try {
      await signOut({ callbackUrl: "/" });
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <div
      className={`bg-white border-r border-gray-200 flex flex-col transition-width duration-300 ease-in-out
            ${open ? "w-64" : "w-16"}`}
    >
      <button
        onClick={onToggle}
        className="p-4 focus:outline-none hover:bg-gray-100 transition-transform duration-300"
        aria-label="Toggle Sidebar"
      >
        <ChevronLeft
          className={`w-6 h-6 transform transition-transform duration-300 ${
            open ? "rotate-0" : "rotate-180"
          }`}
        />
      </button>

      <nav className="flex flex-col mt-4 space-y-2 flex-1">
        <SidebarItem
          icon={<LayoutDashboard />}
          label="Dashboard"
          open={open}
          active={selectedMenu === "dashboard"}
          onClick={() => setSelectedMenu("dashboard")}
        />
        <SidebarItem
          icon={<User />}
          label="Profile"
          open={open}
          active={selectedMenu === "profile"}
          onClick={() => setSelectedMenu("profile")}
        />
        <SidebarItem
          icon={<Briefcase />}
          label="Services"
          open={open}
          active={selectedMenu === "services"}
          onClick={() => setSelectedMenu("services")}
        />
        <SidebarItem
          icon={<PackageIcon />}
          label="Packages"
          open={open}
          active={selectedMenu === "packages"}
          onClick={() => setSelectedMenu("packages")}
        />
        <SidebarItem
          icon={<Building />}
          label="Immigration"
          open={open}
          active={selectedMenu === "immigration"}
          onClick={() => setSelectedMenu("immigration")}
        />
        <SidebarItem
          icon={<BookOpenText />}
          label="Training"
          open={open}
          active={selectedMenu === "training"}
          onClick={() => setSelectedMenu("training")}
        />
        <SidebarItem
          icon={<Star />}
          label="Reviews"
          open={open}
          active={selectedMenu === "reviews"}
          onClick={() => setSelectedMenu("reviews")}
        />
        <SidebarItem
          icon={<MessageCircle />}
          label="Contact Us"
          open={open}
          active={selectedMenu === "contact"}
          onClick={() => setSelectedMenu("contact")}
        />
      </nav>

      {/* Logout Button at Bottom */}
      <div className="mt-auto p-4 border-t border-gray-200">
        <SidebarItem
          icon={<LogOut />}
          label="Logout"
          open={open}
          active={false}
          onClick={handleLogout}
        />
      </div>
    </div>
  );
};

interface SidebarItemProps {
  icon: ReactNode;
  label: string;
  open: boolean;
  active?: boolean;
  onClick: () => void;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  label,
  open,
  active = false,
  onClick,
}) => {
  const [hover, setHover] = useState(false);

  return (
    <div
      onClick={onClick}
      className={`relative flex items-center px-4 py-3 cursor-pointer rounded-md select-none
        ${
          active
            ? "bg-[#404bd0] text-white"
            : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
        }
      `}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      title={!open ? label : undefined}
    >
      <span className="w-5 h-5 flex-shrink-0">{icon}</span>
      {open ? (
        <span className="ml-3">{label}</span>
      ) : (
        hover && (
          <div className="absolute left-full ml-2 whitespace-nowrap rounded bg-gray-900 px-2 py-1 text-sm text-white shadow-lg z-50 select-none">
            {label}
          </div>
        )
      )}
    </div>
  );
};
