"use client";

import React from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import {
  ArrowLeft,
  Calendar,
  User,
  AlertCircle,
  Clock,
  ExternalLink,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import ProcessSteps from "./_components/ProcessSteps";
import applicationStepsData from "@/util/applicationStepsMock.json";

// Type definitions for better type safety
interface ApplicationStep {
  id: number;
  title: string;
  description: string;
  status: "completed" | "in_progress" | "pending" | "not_applicable";
  completedDate?: string;
  scheduledDate?: string;
  estimatedDate?: string;
  startedDate?: string;
  details: Record<string, any>;
}

interface ApplicationData {
  caseId: string;
  caseType: string;
  userName: string;
  caseStatus: string;
  priority: string;
  startDate: string;
  endDate: string;
  currentStep: number;
  steps: ApplicationStep[];
}

const ApplicationPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const caseId = params.caseId as string;

  // Get application data from mock JSON
  const applicationData: ApplicationData | undefined = (
    applicationStepsData as Record<string, ApplicationData>
  )[caseId];

  // Helper function to navigate back to Immigration dashboard
  const handleBackToDashboard = () => {
    router.push("/profile?selectedMenu=immigration");
  };

  // Handle case where application is not found
  if (!applicationData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            onClick={handleBackToDashboard}
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            Back to Dashboard
          </Button>
        </div>
        <Card className="max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-xl font-semibold mb-2">
              Application Not Found
            </h2>
            <p className="text-gray-600 text-center mb-4">
              The application with Case ID &quot;{caseId}&quot; could not be
              found.
            </p>
            <Button onClick={() => router.push("/profile")}>
              Return to Profile
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Helper function to get status badge styling
  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return "default";
      case "closed":
        return "secondary";
      case "pending":
        return "outline";
      default:
        return "default";
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header with Back Button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          onClick={handleBackToDashboard}
          className="flex items-center gap-2 hover:bg-gray-50"
        >
          <ArrowLeft size={16} />
          Back to Dashboard
        </Button>
        <div className="h-6 w-px bg-gray-300" />
        <h1 className="text-2xl font-bold text-gray-900">
          Application Details
        </h1>
      </div>

      {/* Application Overview Card */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <CardTitle className="text-xl font-bold">
                Case ID: {applicationData.caseId}
              </CardTitle>
              <p className="text-gray-600 mt-1">{applicationData.caseType}</p>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={getStatusBadgeVariant(applicationData.caseStatus)}
                className="text-sm px-3 py-1"
              >
                {applicationData.caseStatus}
              </Badge>
              <div className="text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-full">
                Current Stage: Step {applicationData.currentStep} of{" "}
                {applicationData.steps.length}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center gap-3">
              <User className="h-5 w-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Applicant</p>
                <p className="font-medium">{applicationData.userName}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Start Date</p>
                <p className="font-medium">{applicationData.startDate}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Expected End Date</p>
                <p className="font-medium">{applicationData.endDate}</p>
              </div>
            </div>
            {/* Purchase Information Integration */}
            {applicationData.purchaseInfo && (
              <div className="flex items-center gap-3">
                <div className="h-5 w-5 text-gray-500">💳</div>
                <div>
                  <p className="text-sm text-gray-500">Package</p>
                  <p className="font-medium text-sm">
                    {applicationData.purchaseInfo.packageName}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Purchase Details Section */}
          {applicationData.purchaseInfo && (
            <div className="mt-6 pt-6 border-t">
              <h3 className="font-semibold text-gray-900 mb-4">
                Purchase Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {applicationData.purchaseInfo.amount && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-green-600">💰</span>
                      <span className="font-medium text-green-800">
                        Amount Paid
                      </span>
                    </div>
                    <div className="text-lg font-bold text-green-900">
                      {applicationData.purchaseInfo.amount}
                    </div>
                  </div>
                )}
                {applicationData.purchaseInfo.paymentMethod && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-blue-600">💳</span>
                      <span className="font-medium text-blue-800">
                        Payment Method
                      </span>
                    </div>
                    <div className="text-lg font-bold text-blue-900">
                      {applicationData.purchaseInfo.paymentMethod}
                    </div>
                  </div>
                )}
                {applicationData.purchaseInfo.transactionId && (
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-purple-600">🧾</span>
                      <span className="font-medium text-purple-800">
                        Transaction ID
                      </span>
                    </div>
                    <div className="text-sm font-mono text-purple-900 break-all">
                      {applicationData.purchaseInfo.transactionId}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Checkpoint Call Section */}
          {applicationData.checkpointCall && (
            <div className="mt-6 pt-6 border-t">
              <h3 className="font-semibold text-gray-900 mb-4">
                Checkpoint Call
              </h3>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600">📞</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-blue-900">
                        {applicationData.checkpointCall.callType}
                      </h4>
                      <p className="text-sm text-blue-700">
                        Duration: {applicationData.checkpointCall.duration}
                      </p>
                    </div>
                  </div>
                  <Badge
                    variant={
                      applicationData.checkpointCall.status === "completed"
                        ? "default"
                        : applicationData.checkpointCall.status ===
                            "in_progress"
                          ? "secondary"
                          : "outline"
                    }
                    className={
                      applicationData.checkpointCall.status === "completed"
                        ? "bg-green-100 text-green-800"
                        : applicationData.checkpointCall.status ===
                            "in_progress"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                    }
                  >
                    {applicationData.checkpointCall.status === "completed"
                      ? "Completed"
                      : applicationData.checkpointCall.status === "in_progress"
                        ? "In Progress"
                        : "Pending"}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-blue-600 font-medium mb-1">
                      Specialist
                    </p>
                    <p className="text-blue-900">
                      {applicationData.checkpointCall.specialist}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-blue-600 font-medium mb-1">
                      {applicationData.checkpointCall.status === "completed"
                        ? "Completed Date"
                        : "Scheduled Date"}
                    </p>
                    <p className="text-blue-900">
                      {applicationData.checkpointCall.completedDate
                        ? new Date(
                            applicationData.checkpointCall.completedDate
                          ).toLocaleDateString("en-IE", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })
                        : new Date(
                            applicationData.checkpointCall.scheduledDate
                          ).toLocaleDateString("en-IE", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                    </p>
                  </div>
                </div>

                {applicationData.checkpointCall.agenda && (
                  <div className="mb-4">
                    <p className="text-sm text-blue-600 font-medium mb-2">
                      Agenda
                    </p>
                    <ul className="list-disc list-inside space-y-1 text-sm text-blue-800">
                      {applicationData.checkpointCall.agenda.map(
                        (item: string, index: number) => (
                          <li key={index}>{item}</li>
                        )
                      )}
                    </ul>
                  </div>
                )}

                {applicationData.checkpointCall.status !== "completed" &&
                  applicationData.checkpointCall.meetingLink && (
                    <div className="flex gap-3">
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                      >
                        <ExternalLink size={16} />
                        Join Meeting
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                      >
                        <Calendar size={16} />
                        Reschedule
                      </Button>
                    </div>
                  )}
              </div>
            </div>
          )}

          {/* Progress Overview */}
          <div className="mt-6 pt-6 border-t">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900">Overall Progress</h3>
              <span className="text-sm text-gray-600">
                Step {applicationData.currentStep} of{" "}
                {applicationData.steps.length}
              </span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 relative"
                style={{
                  width: `${(applicationData.currentStep / applicationData.steps.length) * 100}%`,
                }}
              >
                <div className="absolute right-0 top-0 h-3 w-3 bg-blue-600 rounded-full transform translate-x-1/2"></div>
              </div>
            </div>

            {/* Status Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">
                  {
                    applicationData.steps.filter(
                      (s) => s.status === "completed"
                    ).length
                  }
                </div>
                <div className="text-xs text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">
                  {
                    applicationData.steps.filter(
                      (s) => s.status === "in_progress"
                    ).length
                  }
                </div>
                <div className="text-xs text-gray-600">In Progress</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-600">
                  {
                    applicationData.steps.filter((s) => s.status === "pending")
                      .length
                  }
                </div>
                <div className="text-xs text-gray-600">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600">
                  {Math.round(
                    (applicationData.currentStep /
                      applicationData.steps.length) *
                      100
                  )}
                  %
                </div>
                <div className="text-xs text-gray-600">Complete</div>
              </div>
            </div>

            {/* Current Step Highlight */}
            {applicationData.steps[applicationData.currentStep - 1] && (
              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-800">
                    Current Step
                  </span>
                </div>
                <h4 className="font-semibold text-blue-900">
                  {applicationData.steps[applicationData.currentStep - 1].title}
                </h4>
                <p className="text-sm text-blue-700 mt-1">
                  {
                    applicationData.steps[applicationData.currentStep - 1]
                      .description
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Process Steps Component */}
      <ProcessSteps
        steps={applicationData.steps}
        currentStep={applicationData.currentStep}
        caseStatus={applicationData.caseStatus}
      />
    </div>
  );
};

export default ApplicationPage;
